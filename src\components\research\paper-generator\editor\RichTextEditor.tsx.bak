import { use<PERSON><PERSON><PERSON>, EditorContent, Editor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Underline from '@tiptap/extension-underline';
import TextStyle from '@tiptap/extension-text-style';
import FontFamily from '@tiptap/extension-font-family';
import Highlight from '@tiptap/extension-highlight';
import Color from '@tiptap/extension-color';
import BulletList from '@tiptap/extension-bullet-list';
import OrderedList from '@tiptap/extension-ordered-list';
import ListItem from '@tiptap/extension-list-item';
import Table from '@tiptap/extension-table';
import TableRow from '@tiptap/extension-table-row';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';
import Link from '@tiptap/extension-link';
import TextAlign from '@tiptap/extension-text-align';
import Typography from '@tiptap/extension-typography';
import Placeholder from '@tiptap/extension-placeholder';
import { ResizableImage } from 'tiptap-extension-resizable-image';
import { useCallback, forwardRef, useImperativeHandle } from 'react';
import ImageAlignment from '../extensions/image-alignment';
import './editor-styles.css';

interface RichTextEditorProps {
  content: string;
  onChange: (content: string) => void;
  onSelectionChange?: () => void;
  className?: string;
  editable?: boolean;
}

interface RichTextEditorRef {
  editor: Editor | null;
  insertContent: (content: string) => void;
  getSelectedText: () => string;
  getSelectionRange: () => { from: number; to: number } | null;
  replaceSelectedText: (text: string) => void;
  applyFormat: (format: string, value?: any) => void;
  getHTML: () => string;
  getText: () => string;
  focus: () => void;
}

const RichTextEditor = forwardRef<RichTextEditorRef, RichTextEditorProps>(
  ({ content, onChange, onSelectionChange, className = '', editable = true }, ref) => {
    // Initialize the editor with rich formatting options
    const editor = useEditor({
      extensions: [
        StarterKit,
        Underline,
        TextStyle,
        FontFamily,
        Highlight.configure({ multicolor: true }),
        Color,
        Link.configure({
          openOnClick: false,
        }),
        TextAlign.configure({
          types: ['heading', 'paragraph'],
        }),
        BulletList.configure({
          HTMLAttributes: {
            class: 'rich-editor-bullet-list',
          },
        }),
        OrderedList.configure({
          HTMLAttributes: {
            class: 'rich-editor-ordered-list',
          },
        }),
        ListItem.configure({
          HTMLAttributes: {
            class: 'rich-editor-list-item',
          },
        }),
        Table.configure({
          resizable: true,
        }),
        TableRow,
        TableHeader,
        TableCell,
        Typography,
        Placeholder.configure({
          placeholder: 'Start writing here...',
        }),
        ResizableImage,
        ImageAlignment,
      ],
      content: content || '',
      editable,
      onUpdate: ({ editor }) => {
        onChange(editor.getHTML());
      },
      onSelectionUpdate: onSelectionChange ? () => onSelectionChange() : undefined,
    });

    // Expose editor methods to parent components
    useImperativeHandle(ref, () => ({
      editor,
      insertContent: (content: string) => {
        editor?.commands.insertContent(content);
      },
      getSelectedText: () => {
        return editor?.state.doc.textBetween(
          editor.state.selection.from,
          editor.state.selection.to,
          ' '
        ) || '';
      },
      getSelectionRange: () => {
        if (!editor?.state.selection) return null;
        return {
          from: editor.state.selection.from,
          to: editor.state.selection.to,
        };
      },
      replaceSelectedText: (text: string) => {
        if (editor?.state.selection) {
          const { from, to } = editor.state.selection;
          editor
            .chain()
            .focus()
            .deleteRange({ from, to })
            .insertContent(text)
            .run();
        }
      },
      applyFormat: (format: string, value?: any) => {
        if (!editor) return;

        // Apply the requested formatting
        switch (format) {
          case 'bold':
            editor.chain().focus().toggleBold().run();
            break;
          case 'italic':
            editor.chain().focus().toggleItalic().run();
            break;
          case 'underline':
            editor.chain().focus().toggleUnderline().run();
            break;
          case 'strike':
            editor.chain().focus().toggleStrike().run();
            break;
          case 'heading':
            editor.chain().focus().toggleHeading({ level: value || 2 }).run();
            break;
          case 'paragraph':
            editor.chain().focus().setParagraph().run();
            break;
          case 'bulletList':
            editor.chain().focus().toggleBulletList().run();
            break;
          case 'orderedList':
            editor.chain().focus().toggleOrderedList().run();
            break;
          case 'blockquote':
            editor.chain().focus().toggleBlockquote().run();
            break;
          case 'codeBlock':
            editor.chain().focus().toggleCodeBlock().run();
            break;
          case 'highlight':
            editor.chain().focus().toggleHighlight({ color: value }).run();
            break;
          case 'color':
            editor.chain().focus().setColor(value).run();
            break;
          case 'align':
            if (['left', 'center', 'right', 'justify'].includes(value)) {
              editor.chain().focus().setTextAlign(value).run();
            }
            break;
          case 'clear':
            editor.chain().focus().unsetAllMarks().run();
            break;
          case 'insertTable':
            editor
              .chain()
              .focus()
              .insertTable({ rows: value?.rows || 3, cols: value?.cols || 3, withHeaderRow: true })
              .run();
            break;
          case 'addColumnBefore':
            editor.chain().focus().addColumnBefore().run();
            break;
          case 'addColumnAfter':
            editor.chain().focus().addColumnAfter().run();
            break;
          case 'deleteColumn':
            editor.chain().focus().deleteColumn().run();
            break;
          case 'addRowBefore':
            editor.chain().focus().addRowBefore().run();
            break;
          case 'addRowAfter':
            editor.chain().focus().addRowAfter().run();
            break;
          case 'deleteRow':
            editor.chain().focus().deleteRow().run();
            break;
          case 'deleteTable':
            editor.chain().focus().deleteTable().run();
            break;
          case 'link':
            if (value) {
              editor.chain().focus().setLink({ href: value }).run();
            } else {
              editor.chain().focus().unsetLink().run();
            }
            break;
          default:
            console.warn(`Unknown format: ${format}`);
        }
      },
      getHTML: () => {
        return editor?.getHTML() || '';
      },
      getText: () => {
        return editor?.state.doc.textContent || '';
      },
      focus: () => {
        editor?.chain().focus().run();
      },
    }));

    return (
      <div className={`rich-text-editor ${className}`}>
        <EditorContent editor={editor} />
      </div>
    );
  }
);

RichTextEditor.displayName = 'RichTextEditor';

export { RichTextEditor };
export type { RichTextEditorRef };
