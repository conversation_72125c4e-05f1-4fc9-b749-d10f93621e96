/* Enhanced Editor Styles */

.ProseMirror {
  outline: none;
  min-height: 600px;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 16px;
  line-height: 1.8;
  color: #374151;
  max-width: none;
  margin: 0 auto;
  transition: all 0.2s ease-in-out;
}

.ProseMirror:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Typography improvements */
.ProseMirror h1 {
  font-size: 2.25rem;
  font-weight: 700;
  line-height: 1.2;
  margin-top: 2rem;
  margin-bottom: 1rem;
  color: #1f2937;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 0.5rem;
}

.ProseMirror h2 {
  font-size: 1.875rem;
  font-weight: 600;
  line-height: 1.3;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  color: #1f2937;
}

.ProseMirror h3 {
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1.4;
  margin-top: 1.25rem;
  margin-bottom: 0.5rem;
  color: #374151;
}

.ProseMirror p {
  margin-bottom: 1rem;
  text-align: justify;
}

.ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #9ca3af;
  pointer-events: none;
  height: 0;
}

/* Enhanced lists */
.ProseMirror ul, .ProseMirror ol {
  margin: 1rem 0;
  padding-left: 2rem;
}

.ProseMirror ul {
  list-style-type: disc;
}

.ProseMirror ol {
  list-style-type: decimal;
}

.ProseMirror li {
  margin-bottom: 0.5rem;
  line-height: 1.6;
  display: list-item;
}

.ProseMirror .rich-editor-bullet-list {
  list-style-type: disc;
  padding-left: 2rem;
}

.ProseMirror .rich-editor-ordered-list {
  list-style-type: decimal;
  padding-left: 2rem;
}

.ProseMirror .rich-editor-list-item {
  display: list-item;
}

/* Enhanced blockquotes */
.ProseMirror blockquote {
  border-left: 4px solid #3b82f6;
  margin: 1.5rem 0;
  padding: 0.5rem 0 0.5rem 1.5rem;
  background-color: #f8fafc;
  border-radius: 0 8px 8px 0;
  font-style: italic;
  color: #4b5563;
}

/* Enhanced code blocks */
.ProseMirror pre {
  background-color: #1f2937;
  color: #e5e7eb;
  padding: 1rem;
  border-radius: 8px;
  margin: 1rem 0;
  overflow-x: auto;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
}

.ProseMirror code {
  background-color: #f3f4f6;
  color: #dc2626;
  padding: 0.125rem 0.25rem;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875em;
}

/* Enhanced tables */
.ProseMirror table {
  border-collapse: collapse;
  table-layout: fixed;
  width: 100%;
  margin: 1.5rem 0;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.ProseMirror td, .ProseMirror th {
  border: 1px solid #e5e7eb;
  padding: 12px;
  vertical-align: top;
  box-sizing: border-box;
  position: relative;
  min-width: 100px;
}

.ProseMirror th {
  background-color: #f9fafb;
  font-weight: 600;
  color: #374151;
}

.ProseMirror .selectedCell:after {
  z-index: 2;
  position: absolute;
  content: "";
  left: 0; right: 0; top: 0; bottom: 0;
  background: rgba(59, 130, 246, 0.1);
  pointer-events: none;
}

.ProseMirror .column-resize-handle {
  position: absolute;
  right: -2px;
  top: 0;
  bottom: -2px;
  width: 4px;
  background-color: #3b82f6;
  pointer-events: none;
}

.ProseMirror.resize-cursor {
  cursor: ew-resize;
  cursor: col-resize;
}

/* Enhanced images */
.ProseMirror img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 1rem 0;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s ease-in-out;
}

.ProseMirror img:hover {
  transform: scale(1.02);
}

.ProseMirror .ProseMirror-selectednode img {
  outline: 3px solid #3b82f6;
  outline-offset: 2px;
}

/* Image alignment */
.ProseMirror .has-alignment-left {
  float: left;
  margin-right: 1.5rem;
  margin-bottom: 1rem;
}

.ProseMirror .has-alignment-right {
  float: right;
  margin-left: 1.5rem;
  margin-bottom: 1rem;
}

.ProseMirror .has-alignment-center {
  display: block;
  margin-left: auto;
  margin-right: auto;
}

/* Image resize handles */
.ProseMirror .resize-handle {
  width: 10px;
  height: 10px;
  background-color: #3b82f6;
  border-radius: 50%;
}

/* Image toolbar */
.image-toolbar {
  position: absolute;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  padding: 4px;
  display: flex;
  gap: 4px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.image-toolbar button {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  background: none;
  border-radius: 4px;
  cursor: pointer;
  color: #4b5563;
}

.image-toolbar button:hover {
  background-color: #f3f4f6;
}

.image-toolbar button.active {
  background-color: #e5e7eb;
  color: #1f2937;
}

/* Enhanced highlighting */
.ProseMirror mark {
  background-color: #fef3c7;
  color: #92400e;
  padding: 0.125rem 0.25rem;
  border-radius: 4px;
}

/* Links */
.ProseMirror a {
  color: #3b82f6;
  text-decoration: underline;
  cursor: pointer;
}

.ProseMirror a:hover {
  color: #1d4ed8;
}

/* Selection styles */
.ProseMirror-selectednode {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
  border-radius: 4px;
}

/* Drag handle for improved UX */
.ProseMirror-gapcursor {
  display: none;
  pointer-events: none;
  position: absolute;
}

.ProseMirror-gapcursor:after {
  content: "";
  display: block;
  position: absolute;
  top: -2px;
  width: 20px;
  border-top: 1px solid #3b82f6;
  animation: ProseMirror-cursor-blink 1.1s steps(2, start) infinite;
}

@keyframes ProseMirror-cursor-blink {
  to {
    visibility: hidden;
  }
}

/* Responsive improvements */
@media (max-width: 768px) {
  .ProseMirror {
    padding: 1rem;
    font-size: 14px;
  }
  
  .ProseMirror h1 {
    font-size: 1.875rem;
  }
  
  .ProseMirror h2 {
    font-size: 1.5rem;
  }
  
  .ProseMirror h3 {
    font-size: 1.25rem;
  }
}

/* Focus and hover improvements */
.ProseMirror .empty-node::before {
  position: absolute;
  color: #9ca3af;
  cursor: text;
}

.ProseMirror .empty-node:hover::before {
  color: #6b7280;
}

/* Better spacing and typography for academic writing */
.ProseMirror {
  line-height: 2;
  letter-spacing: 0.025em;
}

.ProseMirror p + p {
  margin-top: 1.25rem;
}

.ProseMirror h1 + p,
.ProseMirror h2 + p,
.ProseMirror h3 + p {
  margin-top: 0.75rem;
}
