import React from 'react';
import { 
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger 
} from "@/components/ui/accordion";
import { FileText } from "lucide-react";
import { GeneratedSection } from './types';

interface AccordionSectionCardProps {
  section: GeneratedSection;
}

export const AccordionSectionCard: React.FC<AccordionSectionCardProps> = ({ section }) => {
  if (!section.content) return null;
  
  return (
    <Accordion type="single" collapsible className="border border-gray-200 rounded-xl bg-white">
      <AccordionItem value={section.id} className="border-none">
        <AccordionTrigger className="px-6 py-3 hover:no-underline hover:bg-gray-50">
          <div className="flex items-center gap-3">
            <section.icon className="h-5 w-5 text-gray-600" />
            <h3 className="font-semibold text-lg">{section.name}</h3>
          </div>
        </AccordionTrigger>
        <AccordionContent className="px-6 pb-4">
          <div className="text-gray-700 whitespace-pre-line">
            {section.content}
          </div>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
};
