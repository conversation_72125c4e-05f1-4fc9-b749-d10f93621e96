import { LucideIcon } from "lucide-react";

// Paper structure and content types
export interface PaperMetadata {
  title: string;
  researchField: string;
  keywords: string[];
  authors: string[];
}

export interface ContentItem {
  id: string;
  type: 'text' | 'figure';
  content: string;
  order: number;
  title?: string;
  caption?: string;
  aiAnalysis?: string;
}

export interface UserSection {
  id: string;
  name: string;
  items: ContentItem[];
}

export interface UserInputs {
  metadata: PaperMetadata;
  userSections: UserSection[];
}

export interface GeneratedSection {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'generating' | 'completed' | 'error';
  icon: LucideIcon;
  content?: string;
}

export interface SectionType {
  id: string;
  name: string;
  icon: LucideIcon;
  color: string;
  description: string;
  order: number;
  required?: boolean;
}

// AI generation types
export interface AIModelOption {
  id: string;
  name: string;
  provider: string;
  capabilities: string[];
  maxTokens: number;
}

export interface AIGenerationOptions {
  model: string;
  temperature: number;
  maxTokens: number;
  stopSequences?: string[];
}

export interface AIAnalysisResult {
  content: string;
  confidence: number;
  tokens: number;
}
