import {
  <PERSON><PERSON>pen,
  FlaskConical,
  BarChart3,
  Lightbulb,
  PenTool,
  FileText,
  Search
} from "lucide-react";
import { SectionType, AIModelOption } from "./types";

// Research paper section types (following standard research article structure)
export const SECTION_TYPES: SectionType[] = [
  {
    id: 'abstract',
    name: 'Abstract',
    icon: FileText,
    color: 'bg-indigo-500',
    description: 'Concise summary of the research',
    order: 0,
    required: true
  },
  {
    id: 'introduction',
    name: 'Introduction',
    icon: BookOpen,
    color: 'bg-purple-500',
    description: 'Background, problem statement, and objectives',
    order: 1,
    required: true
  },
  {
    id: 'methodology',
    name: 'Methodology',
    icon: FlaskConical,
    color: 'bg-blue-500',
    description: 'Research methods and procedures',
    order: 2,
    required: true
  },
  {
    id: 'results',
    name: 'Results',
    icon: BarChart3,
    color: 'bg-green-500',
    description: 'Findings and data analysis',
    order: 3,
    required: true
  },
  {
    id: 'discussion',
    name: 'Discussion',
    icon: Lightbulb,
    color: 'bg-yellow-500',
    description: 'Interpretation of results and implications',
    order: 4,
    required: true
  },
  {
    id: 'conclusion',
    name: 'Conclusion',
    icon: PenTool,
    color: 'bg-red-500',
    description: 'Summary, limitations, and future work',
    order: 5,
    required: true
  },
  {
    id: 'keywords',
    name: 'Keywords',
    icon: Search,
    color: 'bg-teal-500',
    description: 'Key terms representing the paper content',
    order: -1,
    required: false
  },
  {
    id: 'references',
    name: 'References',
    icon: FileText,
    color: 'bg-gray-500',
    description: 'Citations and references used throughout the paper',
    order: 6,
    required: true
  }
];

// AI generation prompts for each section
export const SECTION_PROMPTS = {
  metadata: (title: string, field: string, keywords: string[]) => 
    `Paper Title: ${title}
     Research Field: ${field}
     Keywords: ${keywords.join(', ')}`,
  
  introduction: (title: string, field: string, keywords: string[], context?: string) =>
    `Create an academic introduction for a research paper titled "${title}" in ${field}. Keywords: ${keywords.join(', ')}.

     ${context ? `Previous context: ${context}` : ''}

     Instructions:
     - Use concise, formal academic language without unnecessary phrases
     - Establish context and background for the research problem
     - Present a clear research problem and motivations
     - Define aims, objectives, and research questions
     - Explain significance and contributions to the field
     - Provide a brief overview of the paper structure
     - Include 3-5 relevant in-text citations in (Author, Year) format
     - Ensure citations are realistic and relevant to ${field}
     - DO NOT include a References section at the end; all references will be compiled separately
     - Avoid phrases like "In this paper, we present" or "Here is an introduction to"
     - Write approximately 400-600 words`,
  
  methodology: (title: string, field: string, userContent: string, introContext?: string) =>
    `Create an academic methodology section for a research paper titled "${title}" in ${field}.

     ${introContext ? `Introduction context: ${introContext}` : ''}

     User-provided context (if any):
     ${userContent}

     Instructions:
     - Use concise, formal academic language without unnecessary phrases
     - Detail research design and approach based on the research objectives
     - Describe data collection methods and procedures
     - Explain participant selection and sampling strategies if applicable
     - Detail analytical techniques and tools used
     - Address ethical considerations if relevant
     - Acknowledge limitations of the methodology
     - Include 2-4 relevant methodological citations in (Author, Year) format
     - Ensure citations are realistic and relevant to ${field} methodology
     - DO NOT include a References section at the end; all references will be compiled separately
     - Avoid phrases like "In this methodology section" or "Here are the methods used"
     - Write approximately 500-700 words`,
  
  results: (title: string, methodologyContent: string, userContent: string, introContext?: string) =>
    `Create an academic results section for a research paper titled "${title}".

     ${introContext ? `Introduction context: ${introContext}` : ''}

     Methodology context:
     ${methodologyContent}

     User-provided results context (if any):
     ${userContent}

     Instructions:
     - Use concise, formal academic language without unnecessary phrases
     - Present findings objectively without interpretation
     - Organize results logically based on the methodology described
     - Include appropriate statistical analyses and data presentation
     - Reference tables and figures in academic style (e.g., "Table 1 shows...")
     - Include 1-3 relevant citations in (Author, Year) format when comparing with other studies
     - Ensure citations are realistic and relevant to the research field
     - DO NOT include a References section at the end; all references will be compiled separately
     - Highlight key findings that address the research questions from the introduction
     - Avoid phrases like "In this section" or "Here are the results"
     - Write approximately 400-600 words`,
  
  discussion: (title: string, resultsContent: string, methodologyContent: string, introContext?: string) =>
    `Create an academic discussion section for a research paper titled "${title}".

     ${introContext ? `Introduction context: ${introContext}` : ''}

     Methodology context:
     ${methodologyContent}

     Results context:
     ${resultsContent}

     Instructions:
     - Use concise, formal academic language without unnecessary phrases
     - Interpret results in context of research questions from the introduction
     - Compare findings with previous research in the field
     - Explain unexpected results and their implications
     - Discuss theoretical and practical implications of the findings
     - Acknowledge limitations of the study
     - Include 3-5 relevant citations in (Author, Year) format when comparing with previous literature
     - Ensure citations are realistic and relevant to the research field
     - DO NOT include a References section at the end; all references will be compiled separately
     - Avoid phrases like "In this discussion" or "Here, we discuss"
     - Write approximately 500-700 words`,
  
  conclusion: (title: string, resultsContent: string, discussionContent: string, introContext?: string) =>
    `Create an academic conclusion section for a research paper titled "${title}".

     ${introContext ? `Introduction context: ${introContext}` : ''}

     Results context:
     ${resultsContent}

     Discussion context:
     ${discussionContent}

     Instructions:
     - Use concise, formal academic language without unnecessary phrases
     - Summarize key findings concisely
     - Revisit research objectives from the introduction
     - Discuss broader implications for the field
     - Suggest specific directions for future research
     - Acknowledge study limitations
     - Include 1-2 relevant citations if discussing future directions
     - Ensure citations are realistic and relevant to the research field
     - DO NOT include a References section at the end; all references will be compiled separately
     - Avoid phrases like "In conclusion" or "To summarize"
     - Write approximately 300-400 words`,
  
  abstract: (title: string, introContent: string, methodologyContent: string, resultsContent: string, conclusionContent: string) =>
    `Create an academic abstract for a research paper titled "${title}".

     Context from paper sections:
     - Introduction: ${introContent}
     - Methodology: ${methodologyContent}
     - Results: ${resultsContent}
     - Conclusion: ${conclusionContent}

     Instructions:
     - Use concise, formal academic language without unnecessary phrases
     - 200-250 words maximum
     - Present background context and research problem
     - Briefly describe methodology approach
     - Highlight key findings and results
     - State main conclusions and implications
     - Do not include citations in the abstract
     - Write as a single paragraph without headings or bullet points
     - Avoid phrases like "This abstract presents" or "In this study"
     - Ensure the abstract can stand alone as a complete summary`,
  
  references: (introContent: string, methodContent: string, resultsContent: string, discussionContent: string, conclusionContent: string, abstractContent: string) =>
    `Create a complete and properly formatted references section for an academic research paper.

    Analyze the following content from the paper to extract all citations that need references:

    Abstract:
    ${abstractContent}

    Introduction:
    ${introContent}

    Methodology:
    ${methodContent}

    Results:
    ${resultsContent}

    Discussion:
    ${discussionContent}

    Conclusion:
    ${conclusionContent}

    CRITICAL INSTRUCTIONS:
    - THIS IS AN AUTOMATED SYSTEM. DO NOT include any instructions or placeholder text in your output.
    - Your output must ONLY consist of properly formatted references in APA 7th edition style.
    - DO NOT include any title, heading, or label like "References:" at the beginning of your output.
    - Extract all in-text citations in the format (Author, Year) from the provided sections.
    - Include only references that were cited in the text.
    - Each reference must include: authors, year, title, source, DOI if available.
    - Alphabetize references by first author's last name.
    - Do not number references.
    - Format each reference with appropriate indentation.
    - For incomplete citations, create plausible complete references based on the subject matter.
    - If no citations are found at all, generate 3-5 relevant references to the research topic.
    - DO NOT include any explanatory text, notes, or instructions in your output.
    - DO NOT include phrases like "Here are the references", "The references for this paper are", etc.
    - DO NOT ask for additional content or clarification.
    - Your response should start directly with the first reference entry.`
};

// Text placeholders for content sections
export const TEXT_PLACEHOLDERS = {
  Methodology:
    "Describe the step-by-step procedures, materials, and data collection methods used in your study...",
  Results:
    "Summarize the key findings, data trends, and statistical analyses that emerged from your research...",
  Introduction:
    "Provide background information and clearly state your research objectives and questions...",
  "Literature Review":
    "Summarize and analyze relevant prior research and theoretical frameworks...",
  Discussion:
    "Interpret the significance of your results and relate them back to your methodology and objectives...",
  Conclusion:
    "Highlight the major takeaways from your work and suggest possible future research directions...",
  Abstract:
    "Provide a concise summary of your research problem, methodology, findings, and implications...",
  Keywords:
    "Enter key terms that represent the main concepts in your research paper...",
  References:
    "All in-text citations from the paper will be automatically extracted and formatted into a properly formatted reference list here..."
};

// Figure placeholders for content sections
export const FIGURE_PLACEHOLDERS = {
  Methodology:
    "Explain what this figure illustrates about your experimental setup or workflow...",
  Results:
    "Describe how this visual represents your data findings or trends...",
  Introduction:
    "Use this caption to provide context that supports your research background...",
  "Literature Review":
    "Explain how this figure relates to the existing literature or theoretical framework...",
  Discussion:
    "Connect this figure to the implications and insights drawn from your results...",
  Conclusion:
    "Optionally summarize key points or future work shown in the figure..."
};

// Empty section prompts
export const EMPTY_SECTION_PROMPTS = {
  Methodology:
    'Add step-by-step procedures, materials used, and any figures illustrating your setup.',
  Results:
    'Include data analyses, tables, figures, and key observations from your study.',
  Introduction:
    'Provide background information, objectives, and any initial figures that frame the study.',
  "Literature Review":
    'Review prior research, theoretical frameworks, and identify research gaps.',
  Discussion:
    'Interpret your findings, compare with prior work, and use figures to highlight important trends.',
  Conclusion:
    'Summarize core findings and suggest future directions or applications.',
  Abstract:
    'Provide a concise overview of your research problem, approach, findings and implications.',
  Keywords:
    'Add 4-6 key terms that represent the main concepts in your research.'
};

// Default prompts for text and figure content items
export const DEFAULT_TEXT_PROMPT = `Enter your detailed analysis here...

• Describe your methodology, findings, or observations
• Include statistical data, measurements, or qualitative insights
• Explain the significance and implications of your work
• Reference any related studies or theoretical frameworks`;

export const DEFAULT_FIGURE_PROMPT = `Provide detailed analysis of this figure...

• What does this figure show or demonstrate?
• Key findings, trends, or patterns visible in the data
• Statistical significance or notable observations
• How does this relate to your research objectives?
• Limitations or considerations when interpreting this data
• Comparison with existing literature or expected results

💡 Tip: Upload your figure with a title and click 'Generate AI Analysis' for automatic analysis!`;

// Available AI models
export const AI_MODELS: AIModelOption[] = [
  {
    id: "google/gemini-2.5-flash-lite-preview-06-17",
    name: "Gemini 2.5 Flash",
    provider: "Google",
    capabilities: ["text", "image analysis"],
    maxTokens: 512
  },
  {
    id: "anthropic/claude-3.5-sonnet",
    name: "Claude 3.5 Sonnet",
    provider: "Anthropic",
    capabilities: ["text", "image analysis"],
    maxTokens: 1024
  },
  {
    id: "openai/gpt-4o",
    name: "GPT-4o",
    provider: "OpenAI",
    capabilities: ["text", "image analysis"],
    maxTokens: 1024
  }
];
