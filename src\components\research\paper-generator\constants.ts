import {
  BookOpen,
  FlaskConical,
  BarChart3,
  Lightbulb,
  PenTool,
  Target,
  FileText,
  Search,
  BookMarked,
  BrainCircuit,
  ListTodo
} from "lucide-react";
import { SectionType, AIModelOption } from "./types";

// Research paper section types
export const SECTION_TYPES: SectionType[] = [
  { 
    id: 'methodology', 
    name: 'Methodology', 
    icon: FlaskConical, 
    color: 'bg-blue-500', 
    description: 'Research methods and procedures',
    order: 2,
    required: false
  },
  { 
    id: 'results', 
    name: 'Results', 
    icon: BarChart3, 
    color: 'bg-green-500', 
    description: 'Findings and data analysis',
    order: 3,
    required: false
  },
  { 
    id: 'introduction', 
    name: 'Introduction', 
    icon: BookOpen, 
    color: 'bg-purple-500', 
    description: 'Background, problem statement, and objectives',
    order: 1,
    required: false
  },
  { 
    id: 'literature-review', 
    name: 'Literature Review', 
    icon: BookMarked, 
    color: 'bg-orange-500', 
    description: 'Related work and theoretical background',
    order: 1.5,
    required: false
  },
  { 
    id: 'discussion', 
    name: 'Discussion', 
    icon: Lightbulb, 
    color: 'bg-yellow-500', 
    description: 'Interpretation of results and implications',
    order: 4,
    required: false
  },
  { 
    id: 'conclusion', 
    name: 'Conclusion', 
    icon: PenTool, 
    color: 'bg-red-500', 
    description: 'Summary, limitations, and future work',
    order: 5,
    required: false
  },
  { 
    id: 'abstract', 
    name: 'Abstract', 
    icon: Target, 
    color: 'bg-indigo-500', 
    description: 'Concise summary of the entire research',
    order: 6,
    required: false
  },
  { 
    id: 'keywords', 
    name: 'Keywords', 
    icon: Search, 
    color: 'bg-teal-500', 
    description: 'Key terms representing the paper content',
    order: 0,
    required: false
  },
  { 
    id: 'references', 
    name: 'References', 
    icon: FileText, 
    color: 'bg-gray-500', 
    description: 'Citations and references used throughout the paper',
    order: 7,
    required: false
  }
];

// AI generation prompts for each section
export const SECTION_PROMPTS = {
  metadata: (title: string, field: string, keywords: string[]) => 
    `Paper Title: ${title}
     Research Field: ${field}
     Keywords: ${keywords.join(', ')}`,
  
  introduction: (title: string, field: string, keywords: string[]) => 
    `Create an academic introduction for a research paper titled "${title}" in ${field}. Keywords: ${keywords.join(', ')}.
     
     Instructions:
     - Use concise, formal academic language without unnecessary phrases
     - Establish context and background
     - Present a clear research problem and motivations
     - Define aims, objectives, and research questions
     - Explain significance and contributions
     - Provide a brief overview of paper structure
     - Use in-text citations (Author, Year) format for all references
     - DO NOT include a References section at the end; all references will be compiled separately
     - Avoid phrases like "In this paper, we present" or "Here is an introduction to"`,
  
  literature_review: (title: string, field: string, keywords: string[]) => 
    `Create an academic literature review for a research paper titled "${title}" in ${field}. Keywords: ${keywords.join(', ')}.
     
     Instructions:
     - Use concise, formal academic language without unnecessary phrases
     - Synthesize relevant prior work
     - Identify theoretical frameworks and methodologies in related research
     - Highlight research gaps
     - Explain how this work addresses those gaps
     - Use in-text citations (Author, Year) format for all references
     - DO NOT include a References section at the end; all references will be compiled separately
     - Organize content thematically or chronologically as appropriate
     - Avoid phrases like "In this literature review" or "Here is a review of"`,
  
  methodology: (title: string, field: string, userContent: string) => 
    `Create an academic methodology section for a research paper titled "${title}" in ${field}.
     
     Context (if provided):
     ${userContent}
     
     Instructions:
     - Use concise, formal academic language without unnecessary phrases
     - Detail research design and approach
     - Describe data collection methods and procedures
     - Explain participant selection and sampling strategies if applicable
     - Detail analytical techniques
     - Address ethical considerations
     - Acknowledge limitations
     - Use in-text citations (Author, Year) format for any methodological references
     - DO NOT include a References section at the end; all references will be compiled separately
     - Avoid phrases like "In this methodology section" or "Here are the methods used"`,
  
  results: (title: string, methodologyContent: string, userContent: string) => 
    `Create an academic results section for a research paper titled "${title}".
     
     Methodology context:
     ${methodologyContent}
     
     Results context (if provided):
     ${userContent}
     
     Instructions:
     - Use concise, formal academic language without unnecessary phrases
     - Present findings objectively without interpretation
     - Organize results logically based on the methodology
     - Include appropriate statistical analyses
     - Reference tables and figures in academic style
     - Use in-text citations (Author, Year) format if comparing with other studies
     - DO NOT include a References section at the end; all references will be compiled separately
     - Highlight key findings addressing research questions
     - Avoid phrases like "In this section" or "Here are the results"`,
  
  discussion: (title: string, resultsContent: string, methodologyContent: string) => 
    `Create an academic discussion section for a research paper titled "${title}".
     
     Methodology context:
     ${methodologyContent}
     
     Results context:
     ${resultsContent}
     
     Instructions:
     - Use concise, formal academic language without unnecessary phrases
     - Interpret results in context of research questions
     - Compare findings with previous research
     - Explain unexpected results
     - Discuss theoretical and practical implications
     - Acknowledge limitations
     - Use in-text citations (Author, Year) format when comparing with previous literature
     - DO NOT include a References section at the end; all references will be compiled separately
     - Avoid phrases like "In this discussion" or "Here, we discuss"`,
  
  conclusion: (title: string, resultsContent: string, discussionContent: string) =>
    `Create an academic conclusion section for a research paper titled "${title}".
     
     Results context:
     ${resultsContent}
     
     Discussion context:
     ${discussionContent}
     
     Instructions:
     - Use concise, formal academic language without unnecessary phrases
     - Summarize key findings concisely
     - Revisit research objectives
     - Discuss broader implications for the field
     - Suggest directions for future research
     - Use minimal in-text citations, focusing on synthesizing the work
     - DO NOT include a References section at the end; all references will be compiled separately
     - Avoid phrases like "In conclusion" or "To summarize"`,
  
  abstract: (title: string, introContent: string, methodologyContent: string, resultsContent: string, conclusionContent: string) =>
    `Create an academic abstract for a research paper titled "${title}".
     
     Context from paper sections:
     - Introduction: ${introContent}
     - Methodology: ${methodologyContent}
     - Results: ${resultsContent}
     - Conclusion: ${conclusionContent}
     
     Instructions:
     - Use concise, formal academic language without unnecessary phrases
     - 200-250 words maximum
     - Present background context and research problem
     - Briefly describe methodology
     - Highlight key findings
     - State main conclusions and implications
     - Do not include citations in the abstract
     - Write as a single paragraph without headings or bullet points
     - Avoid phrases like "This abstract presents" or "In this study"`,
     
  references: (introContent: string, litReviewContent: string, methodContent: string, resultsContent: string, discussionContent: string, conclusionContent: string) =>
    `Create a complete and properly formatted references section for an academic research paper.
    
    Analyze the following content from the paper to extract all citations that need references:
    
    Introduction:
    ${introContent}
    
    Literature Review:
    ${litReviewContent}
    
    Methodology:
    ${methodContent}
    
    Results:
    ${resultsContent}
    
    Discussion:
    ${discussionContent}
    
    Conclusion:
    ${conclusionContent}
    
    CRITICAL INSTRUCTIONS:
    - THIS IS AN AUTOMATED SYSTEM. DO NOT include any instructions or placeholder text in your output.
    - Your output must ONLY consist of properly formatted references in APA 7th edition style.
    - DO NOT include any title, heading, or label like "References:" at the beginning of your output.
    - Extract all in-text citations in the format (Author, Year) from the provided sections.
    - Include only references that were cited in the text.
    - Each reference must include: authors, year, title, source, DOI if available.
    - Alphabetize references by first author's last name.
    - Do not number references.
    - Format each reference with appropriate indentation.
    - For incomplete citations, create plausible complete references based on the subject matter.
    - If no citations are found at all, generate 3-5 relevant references to "${methodContent ? 'the methodology described' : 'artificial intelligence in research paper writing'}".
    - DO NOT include any explanatory text, notes, or instructions in your output.
    - DO NOT include phrases like "Here are the references", "The references for this paper are", etc.
    - DO NOT ask for additional content or clarification.
    - Your response should start directly with the first reference entry.`
};

// Text placeholders for content sections
export const TEXT_PLACEHOLDERS = {
  Methodology:
    "Describe the step-by-step procedures, materials, and data collection methods used in your study...",
  Results:
    "Summarize the key findings, data trends, and statistical analyses that emerged from your research...",
  Introduction:
    "Provide background information and clearly state your research objectives and questions...",
  "Literature Review":
    "Summarize and analyze relevant prior research and theoretical frameworks...",
  Discussion:
    "Interpret the significance of your results and relate them back to your methodology and objectives...",
  Conclusion:
    "Highlight the major takeaways from your work and suggest possible future research directions...",
  Abstract:
    "Provide a concise summary of your research problem, methodology, findings, and implications...",
  Keywords:
    "Enter key terms that represent the main concepts in your research paper...",
  References:
    "All in-text citations from the paper will be automatically extracted and formatted into a properly formatted reference list here..."
};

// Figure placeholders for content sections
export const FIGURE_PLACEHOLDERS = {
  Methodology:
    "Explain what this figure illustrates about your experimental setup or workflow...",
  Results:
    "Describe how this visual represents your data findings or trends...",
  Introduction:
    "Use this caption to provide context that supports your research background...",
  "Literature Review":
    "Explain how this figure relates to the existing literature or theoretical framework...",
  Discussion:
    "Connect this figure to the implications and insights drawn from your results...",
  Conclusion:
    "Optionally summarize key points or future work shown in the figure..."
};

// Empty section prompts
export const EMPTY_SECTION_PROMPTS = {
  Methodology:
    'Add step-by-step procedures, materials used, and any figures illustrating your setup.',
  Results:
    'Include data analyses, tables, figures, and key observations from your study.',
  Introduction:
    'Provide background information, objectives, and any initial figures that frame the study.',
  "Literature Review":
    'Review prior research, theoretical frameworks, and identify research gaps.',
  Discussion:
    'Interpret your findings, compare with prior work, and use figures to highlight important trends.',
  Conclusion:
    'Summarize core findings and suggest future directions or applications.',
  Abstract:
    'Provide a concise overview of your research problem, approach, findings and implications.',
  Keywords:
    'Add 4-6 key terms that represent the main concepts in your research.'
};

// Default prompts for text and figure content items
export const DEFAULT_TEXT_PROMPT = `Enter your detailed analysis here...

• Describe your methodology, findings, or observations
• Include statistical data, measurements, or qualitative insights
• Explain the significance and implications of your work
• Reference any related studies or theoretical frameworks`;

export const DEFAULT_FIGURE_PROMPT = `Provide detailed analysis of this figure...

• What does this figure show or demonstrate?
• Key findings, trends, or patterns visible in the data
• Statistical significance or notable observations
• How does this relate to your research objectives?
• Limitations or considerations when interpreting this data
• Comparison with existing literature or expected results

💡 Tip: Upload your figure with a title and click 'Generate AI Analysis' for automatic analysis!`;

// Available AI models
export const AI_MODELS: AIModelOption[] = [
  {
    id: "google/gemini-2.5-flash-lite-preview-06-17",
    name: "Gemini 2.5 Flash",
    provider: "Google",
    capabilities: ["text", "image analysis"],
    maxTokens: 512
  },
  {
    id: "anthropic/claude-3.5-sonnet",
    name: "Claude 3.5 Sonnet",
    provider: "Anthropic",
    capabilities: ["text", "image analysis"],
    maxTokens: 1024
  },
  {
    id: "openai/gpt-4o",
    name: "GPT-4o",
    provider: "OpenAI",
    capabilities: ["text", "image analysis"],
    maxTokens: 1024
  }
];
