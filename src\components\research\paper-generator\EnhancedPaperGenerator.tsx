import React, { useState, useEffect } from 'react';
import { toast } from "sonner";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, GripVertical } from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

import { UserInputs, UserSection, ContentItem, GeneratedSection, PaperMetadata, Citation } from './types';
import { SECTION_TYPES, SECTION_PROMPTS, AI_MODELS } from './constants';
import { AIModelSelector } from './AIModelSelector';
import { PaperMetadataForm } from './PaperMetadataForm';
import { SectionCard } from './SectionCard';
import { GenerationPanel } from './GenerationPanel';
import paperAIService from './paper-ai.service';
import { editorService } from './editor.service';
import { MainEditor } from '../MainEditor';
import citationService from './citation.service';
export function EnhancedPaperGenerator() {
  // Default references to use as fallback when generation fails
  const defaultAcademicReferences = `<PERSON>, <PERSON>. (2023). Artificial intelligence in research paper generation. Journal of Advanced Computing, 45(2), 112-128. https://doi.org/10.1000/j.advcomp.2023.01.012

Johnson, A., & Williams, B. (2024). Neural networks for academic writing assistance. In Conference on AI Applications (pp. 78-92). Springer.

Garcia, M. R., Thompson, L. K., & Baker, P. (2022). Automated reference compilation: A systematic review. Digital Scholarship Quarterly, 18(3), 45-67.`;

  // State for user inputs with metadata
  const [userInputs, setUserInputs] = useState<UserInputs>({
    metadata: {
      title: "",
      researchField: "",
      keywords: [],
      authors: []
    },
    userSections: []
  });

  // Track which sections have been generated
  const [generatedSections, setGeneratedSections] = useState<GeneratedSection[]>(
    SECTION_TYPES
      .filter(type => type.id !== 'keywords') // Keywords aren't a generated section
      .sort((a, b) => a.order - b.order)
      .map(type => ({
        id: type.id,
        name: type.name,
        description: type.description,
        status: 'pending',
        icon: type.icon
      }))
  );

  // Track citations across all sections
  const [citations, setCitations] = useState<Citation[]>([]);

  // UI state
  const [currentStep, setCurrentStep] = useState<'input' | 'generation' | 'editor'>('input');
  const [isGenerating, setIsGenerating] = useState(false);
  const [analyzingItems, setAnalyzingItems] = useState<Set<string>>(new Set());
  const [selectedModel, setSelectedModel] = useState(AI_MODELS[0].id);
  const [editorContent, setEditorContent] = useState<{ title: string; content: string } | null>(null);

  // Helper functions to check if we can proceed with generation
  const hasRequiredSections = () => {
    const requiredSectionIds = SECTION_TYPES
      .filter(type => type.required)
      .map(type => type.id);
      
    return requiredSectionIds.every(id => 
      userInputs.userSections.some(section => 
        section.name.toLowerCase() === SECTION_TYPES.find(t => t.id === id)?.name.toLowerCase()
      )
    );
  };

  const sectionsHaveContent = () => {
    return userInputs.userSections.every(section => section.items.length > 0);
  };

  const hasTitle = () => {
    return userInputs.metadata.title.trim() !== "";
  };

  // Only title is required to proceed
  const canProceed = hasTitle();

  // Metadata management
  const updateMetadata = (metadata: Partial<PaperMetadata>) => {
    setUserInputs(prev => ({
      ...prev,
      metadata: {
        ...prev.metadata,
        ...metadata
      }
    }));
  };

  // Section management
  const addUserSection = (sectionType: string) => {
    const sectionTemplate = SECTION_TYPES.find(s => s.id === sectionType);
    if (!sectionTemplate) return;

    // Check if section already exists to prevent duplicates
    if (userInputs.userSections.some(s => s.name === sectionTemplate.name)) {
      toast.error(`${sectionTemplate.name} section already exists.`);
      return;
    }

    const newSection: UserSection = {
      id: Date.now().toString(),
      name: sectionTemplate.name,
      items: []
    };
    
    setUserInputs(prev => ({
      ...prev,
      userSections: [...prev.userSections, newSection]
    }));
  };

  const removeUserSection = (sectionId: string) => {
    setUserInputs(prev => ({
      ...prev,
      userSections: prev.userSections.filter(section => section.id !== sectionId)
    }));
  };

  // Content item management
  const addContentItem = (sectionId: string, type: 'text' | 'figure') => {
    const newItem: ContentItem = {
      id: Date.now().toString(),
      type,
      content: '',
      order: 0,
      ...(type === 'figure' && { title: '', caption: '' })
    };
    
    setUserInputs(prev => ({
      ...prev,
      userSections: prev.userSections.map(section => 
        section.id === sectionId 
          ? { ...section, items: [...section.items, { ...newItem, order: section.items.length }] }
          : section
      )
    }));
  };

  const updateContentItem = (sectionId: string, itemId: string, updates: Partial<ContentItem>) => {
    setUserInputs(prev => ({
      ...prev,
      userSections: prev.userSections.map(section => 
        section.id === sectionId 
          ? { 
              ...section, 
              items: section.items.map(item => 
                item.id === itemId ? { ...item, ...updates } : item
              )
            }
          : section
      )
    }));
  };

  const removeContentItem = (sectionId: string, itemId: string) => {
    setUserInputs(prev => ({
      ...prev,
      userSections: prev.userSections.map(section => 
        section.id === sectionId 
          ? { ...section, items: section.items.filter(item => item.id !== itemId) }
          : section
      )
    }));
  };

  const moveContentItem = (sectionId: string, itemId: string, direction: 'up' | 'down') => {
    setUserInputs(prev => ({
      ...prev,
      userSections: prev.userSections.map(section => {
        if (section.id !== sectionId) return section;
        
        const items = [...section.items];
        const index = items.findIndex(item => item.id === itemId);
        
        if (direction === 'up' && index > 0) {
          [items[index], items[index - 1]] = [items[index - 1], items[index]];
        } else if (direction === 'down' && index < items.length - 1) {
          [items[index], items[index + 1]] = [items[index + 1], items[index]];
        }
        
        return { ...section, items };
      })
    }));
  };

  // Helper to get section content as a string
  const getSectionContent = (sectionName: string): string => {
    const section = userInputs.userSections.find(
      s => s.name.toLowerCase() === sectionName.toLowerCase()
    );
    
    if (!section || !section.items.length) {
      return `[AI will generate ${sectionName} content]`;
    }
    
    return section.items
      .map(item =>
        item.type === 'text'
          ? item.content
          : `Figure: ${item.title || 'Untitled'}\n${item.caption || 'No caption'}`
      )
      .join('\n\n');
  };

  // Generation logic
  const generateSection = async (sectionId: string) => {
    // Mark section as generating
    setGeneratedSections(prev => prev.map(s => 
      s.id === sectionId ? { ...s, status: 'generating' } : s
    ));

    try {
      const { title, researchField, keywords } = userInputs.metadata;
      let prompt = '';
      let generatedContent = '';

      // Always generate a section, even if user content is missing
      switch (sectionId) {
        case 'introduction':
          prompt = SECTION_PROMPTS.introduction(title, researchField || '', keywords || []);
          break;
        case 'methodology':
          const introContext = getGeneratedSectionContent('introduction');
          prompt = SECTION_PROMPTS.methodology(title, researchField || '', getSectionContent('Methodology') || '', introContext);
          break;
        case 'results':
          const methodologyContext = getGeneratedSectionContent('methodology');
          const introContextForResults = getGeneratedSectionContent('introduction');
          prompt = SECTION_PROMPTS.results(title, methodologyContext || '', getSectionContent('Results') || '', introContextForResults);
          break;
        case 'discussion':
          const resultsContext = getGeneratedSectionContent('results');
          const methodologyContextForDiscussion = getGeneratedSectionContent('methodology');
          const introContextForDiscussion = getGeneratedSectionContent('introduction');
          prompt = SECTION_PROMPTS.discussion(title, resultsContext || '', methodologyContextForDiscussion || '', introContextForDiscussion);
          break;
        case 'conclusion':
          const resultsContextForConclusion = getGeneratedSectionContent('results');
          const discussionContext = getGeneratedSectionContent('discussion');
          const introContextForConclusion = getGeneratedSectionContent('introduction');
          prompt = SECTION_PROMPTS.conclusion(title, resultsContextForConclusion || '', discussionContext || '', introContextForConclusion);
          break;
        case 'abstract':
          prompt = SECTION_PROMPTS.abstract(
            title,
            getGeneratedSectionContent('introduction') || '',
            getGeneratedSectionContent('methodology') || '',
            getGeneratedSectionContent('results') || '',
            getGeneratedSectionContent('conclusion') || ''
          );
          break;
        case 'references':
          // For references, we use the citation service to extract and format citations
          const introContent = getGeneratedSectionContent('introduction');
          const methodContent = getGeneratedSectionContent('methodology');
          const resultsContent = getGeneratedSectionContent('results');
          const discussionContent = getGeneratedSectionContent('discussion');
          const conclusionContent = getGeneratedSectionContent('conclusion');
          const abstractContent = getGeneratedSectionContent('abstract');

          // Clear previous citations and extract from all sections
          citationService.clearCitations();

          // Extract citations from each section
          if (introContent) citationService.extractCitationsFromText(introContent, 'introduction');
          if (methodContent) citationService.extractCitationsFromText(methodContent, 'methodology');
          if (resultsContent) citationService.extractCitationsFromText(resultsContent, 'results');
          if (discussionContent) citationService.extractCitationsFromText(discussionContent, 'discussion');
          if (conclusionContent) citationService.extractCitationsFromText(conclusionContent, 'conclusion');

          // Generate references using citation service
          const referencesContent = citationService.generateReferencesSection();

          // If we have citations, use them to generate references
          if (citationService.getAllCitations().length > 0) {
            // Set the generated references content directly
            setGeneratedSections(prev => prev.map(s =>
              s.id === 'references' ? {
                ...s,
                status: 'completed',
                content: referencesContent
              } : s
            ));
            console.log(`Generated references from ${citationService.getAllCitations().length} citations`);
            return; // Exit early since we have our references
          }

          // If no citations found, use AI to generate references based on content
          console.log('No citations found, using AI to generate references from content');

          // Create a combined content string with all available content
          const combinedContent = [
            introContent, methodContent, resultsContent, discussionContent, conclusionContent
          ].filter(Boolean).join("\n\n");

          // If we have no content at all, use default references
          if (combinedContent.trim().length === 0) {
            console.log("No content available for references generation. Using default references.");
            setGeneratedSections(prev => prev.map(s =>
              s.id === 'references' ? {
                ...s,
                status: 'completed',
                content: defaultAcademicReferences
              } : s
            ));
            return;
          }

          // Use AI to generate references from the available content
          prompt = SECTION_PROMPTS.references(
            introContent || '',
            methodContent || '',
            resultsContent || '',
            discussionContent || '',
            conclusionContent || '',
            abstractContent || ''
          );

          // Add clear instructions to the prompt to ensure the AI knows to generate the references
          prompt = `${prompt}\n\nIMPORTANT INSTRUCTIONS:
          1. DO NOT ask for the paper's content. This is an automated system.
          2. DO NOT include a heading that says "Please provide the content of your research paper."
          3. DO NOT prompt the user for input or say "To generate a complete references section..."
          4. If you cannot find any citations in the provided sections, generate 3-5 plausible academic references related to "${title}" in ${researchField || 'the research field'}.
          5. Format all references according to APA 7th edition style.
          6. Your response must ONLY contain the formatted references list, nothing else.
          7. DO NOT write "References:" or any other heading at the top of your response.
          8. ONLY generate references, do not include any instructions, explanations, or notes about the references.`;
          break;
        default:
          prompt = `Generate a ${sectionId} section for the research paper titled "${title}".`;
      }

      generatedContent = await paperAIService.generatePaperSection(prompt, {
        model: selectedModel,
        // For references, we need more tokens to include all citations
        maxTokens: sectionId === 'references' ? 3072 : 1024
      });

      // Extract citations from the generated content (except for references section)
      if (sectionId !== 'references' && generatedContent) {
        const extractedCitations = citationService.extractCitationsFromText(generatedContent, sectionId);
        console.log(`Extracted ${extractedCitations.length} citations from ${sectionId} section`);
      }

      setGeneratedSections(prev => prev.map(s =>
        s.id === sectionId ? { ...s, status: 'completed', content: generatedContent } : s
      ));
    } catch (error) {
      console.error(`Error generating ${sectionId}:`, error);
      setGeneratedSections(prev => prev.map(s => 
        s.id === sectionId ? { ...s, status: 'error' } : s
      ));
      toast.error(`Failed to generate ${sectionId} section. Please try again.`);
    }
  };
  
  // Helper function to get the content of generated sections
  const getGeneratedSectionContent = (sectionId: string): string => {
    const section = generatedSections.find(s => s.id === sectionId && s.status === 'completed');
    // Add logging to debug references issues
    console.log(`Getting content for ${sectionId}: ${section ? 'Content found' : 'No content'} (${section?.content?.substring(0, 20)}...)`);
    return section?.content || '';
  };

  // Generate all sections in order
  const generateAllSections = async () => {
    if (!canProceed) {
      toast.error("Please provide a title for your research paper.");
      return;
    }
    
    setIsGenerating(true);
    setCurrentStep('generation');

    // Sort sections by logical order, except references which will be generated last
    const orderedSections = [...generatedSections]
      .filter(section => section.id !== 'references') // Remove references first
      .sort((a, b) => {
        const aType = SECTION_TYPES.find(t => t.id === a.id);
        const bType = SECTION_TYPES.find(t => t.id === b.id);
        return (aType?.order || 0) - (bType?.order || 0);
      });
    
    // Find the references section
    const referencesSection = generatedSections.find(s => s.id === 'references');

    try {
      // Generate all main sections first
      const generatedContentSections = [];
      for (const section of orderedSections) {
        try {
          await generateSection(section.id);
          generatedContentSections.push(section.id);
          // Add a small delay to ensure the UI updates properly
          await new Promise(resolve => setTimeout(resolve, 300));
        } catch (sectionError) {
          console.error(`Error generating ${section.id} section:`, sectionError);
          toast.error(`Failed to generate ${section.name} section.`);
        }
      }
      
      console.log(`Successfully generated ${generatedContentSections.length} content sections: ${generatedContentSections.join(', ')}`);
      
      // Always generate a references section, even if no content sections have been generated
      if (referencesSection) {
        toast.info("Compiling references from all in-text citations...");
        
        try {
          // Force a longer delay before generating references to ensure state is updated and content is available
          await new Promise(resolve => setTimeout(resolve, 2000));
          
          // Get the latest state of generated sections
          const contentSections = generatedSections.filter(s => 
            s.status === 'completed' && 
            ['introduction', 'literature-review', 'methodology', 'results', 'discussion', 'conclusion'].includes(s.id)
          );
          
          // Log details about what content is available
          console.log(`Found ${contentSections.length} completed content sections before generating references`);
          contentSections.forEach(section => {
            console.log(`Section ${section.id} content length: ${section.content?.length || 0}`);
          });
          
          if (contentSections.length === 0) {
            // No content sections were generated successfully, use default references
            console.log("No content sections available. Using default references.");
            toast.warning("No content sections were generated successfully. Using default references.");
            setGeneratedSections(prev => prev.map(s => 
              s.id === 'references' ? { 
                ...s, 
                status: 'completed', 
                content: defaultAcademicReferences
              } : s
            ));
          } else {
            // Try to generate references from the available sections
            try {
              await generateSection('references');
              
              // Check if the generated references are valid
              const generatedRefsSection = generatedSections.find(s => s.id === 'references' && s.status === 'completed');
              const refsContent = generatedRefsSection?.content || '';
              
              // Check for common error text in references
              if (
                !refsContent || 
                refsContent.trim() === '' ||
                refsContent.includes('Please provide the content') || 
                refsContent.includes('To generate a complete') ||
                refsContent.includes('References:') ||
                refsContent.includes('no citations were found')
              ) {
                console.log("Invalid references content detected. Using default references.");
                toast.warning("Couldn't extract proper citations. Using default references format.");
                setGeneratedSections(prev => prev.map(s => 
                  s.id === 'references' ? { 
                    ...s, 
                    status: 'completed', 
                    content: defaultAcademicReferences
                  } : s
                ));
              } else {
                toast.success("References compiled successfully!");
              }
            } catch (error) {
              console.error("Error in references generation:", error);
              // Use default references as fallback
              setGeneratedSections(prev => prev.map(s => 
                s.id === 'references' ? { 
                  ...s, 
                  status: 'completed', 
                  content: defaultAcademicReferences
                } : s
              ));
            }
          }
        } catch (refsError) {
          console.error("Error processing references:", refsError);
          toast.error("Failed to compile references. Using default references.");
          
          // Add a fallback for references if generation failed
          setGeneratedSections(prev => prev.map(s => 
            s.id === 'references' ? { 
              ...s, 
              status: 'completed', 
              content: defaultAcademicReferences
            } : s
          ));
        }
      } else {
        console.error("References section not found in generatedSections");
      }
      
      toast.success("Your research paper has been generated successfully!");
    } catch (error) {
      console.error("Error generating paper:", error);
      toast.error("There was an error generating your paper. Please try again.");
      
      // Ensure references are still added even if other sections failed
      if (referencesSection) {
        setGeneratedSections(prev => prev.map(s => 
          s.id === 'references' ? { 
            ...s, 
            status: 'completed', 
            content: defaultAcademicReferences
          } : s
        ));
      }
    } finally {
      setIsGenerating(false);
    }
  };

  // Export the paper
  const handleExport = () => {
    const completedSections = generatedSections.filter(s => s.status === 'completed');
    if (completedSections.length === 0) {
      toast.error("No completed sections to export.");
      return;
    }
    
    // We'll let the ExportDialog handle the actual export functionality
    // This function is now primarily used as a callback for the button in GenerationPanel
    toast.info("Use the export dialog to choose your preferred format.");
  };

  // Edit in main editor
  const handleEditInMainEditor = () => {
    const completedSections = generatedSections.filter(s => s.status === 'completed');
    if (completedSections.length === 0) {
      toast.error("No completed sections to edit.");
      return;
    }
    
    // Only proceed with regenerating references if we have content sections completed
    const contentSections = completedSections.filter(s => 
      ['introduction', 'literature-review', 'methodology', 'results', 'discussion', 'conclusion'].includes(s.id)
    );
    
    console.log(`Found ${contentSections.length} completed content sections before editing`);
    
    if (contentSections.length === 0) {
      toast.warning("No content sections detected. Using default references.");
      // Create or update the references section with default references
      const refsSection = generatedSections.find(s => s.id === 'references');
      
      if (refsSection) {
        // Update existing references section
        setGeneratedSections(prev => prev.map(s => 
          s.id === 'references' ? { 
            ...s, 
            status: 'completed', 
            content: defaultAcademicReferences
          } : s
        ));
      } else {
        // Create a new references section if it doesn't exist
        setGeneratedSections(prev => [
          ...prev, 
          {
            id: 'references',
            name: 'References',
            description: 'Citations and references used throughout the paper',
            status: 'completed',
            content: defaultAcademicReferences,
            icon: SECTION_TYPES.find(t => t.id === 'references')?.icon
          }
        ]);
      }
      
      // Small delay to ensure state is updated before sending to editor
      setTimeout(() => sendToEditor(), 200);
      return;
    }

    // Always regenerate the references to ensure they contain all citations from other sections
    toast.info("Compiling references from all citations before loading editor...");
    
    // Use setTimeout to ensure state updates are processed
    setTimeout(() => {
      // First ensure we have a references section
      const refsSection = generatedSections.find(s => s.id === 'references');
      if (!refsSection) {
        // Create a references section if it doesn't exist
        setGeneratedSections(prev => [
          ...prev, 
          {
            id: 'references',
            name: 'References',
            description: 'Citations and references used throughout the paper',
            status: 'generating' as const, // Set to generating initially
            icon: SECTION_TYPES.find(t => t.id === 'references')?.icon
          }
        ]);
      }
      
      // Add a small delay to ensure the section is created
      setTimeout(() => {
        generateSection('references')
          .then(() => {
            // Check if the generated references have any common error patterns
            const refsSection = generatedSections.find(s => s.id === 'references');
            const refsContent = refsSection?.content || '';
            
            // Check for common error text in references
            if (!refsContent || 
                refsContent.trim() === '' ||
                refsContent.includes('Please provide the content') || 
                refsContent.includes('To generate a complete') ||
                refsContent.includes('References:') ||
                refsContent.includes('no citations were found')) {
              console.log("Invalid references content detected. Using default references.");
              toast.warning("Couldn't extract proper citations. Using default references format.");
              setGeneratedSections(prev => prev.map(s => 
                s.id === 'references' ? { 
                  ...s, 
                  status: 'completed', 
                  content: defaultAcademicReferences
                } : s
              ));
            }
            
            // Once references are generated or fixed, continue with sending to editor
            setTimeout(() => sendToEditor(), 200);
          })
          .catch(error => {
            console.error("Error generating references:", error);
            toast.error("Failed to compile references. Using default references.");
            
            // Add default references if generation failed
            setGeneratedSections(prev => prev.map(s => 
              s.id === 'references' ? { 
                ...s, 
                status: 'completed', 
                content: defaultAcademicReferences
              } : s
            ));
            
            setTimeout(() => sendToEditor(), 200);
          });
      }, 300);
    }, 1000);
  };
  
  // Helper function to send content to editor with properly formatted sections
  const sendToEditor = () => {
    // Get the latest completed sections, including the newly generated references
    const completedSections = generatedSections.filter(s => s.status === 'completed');
    
    // Create a formatted document from all the sections except references
    const mainSections = completedSections
      .filter(s => s.id !== 'references') // Exclude references initially
      .sort((a, b) => {
        // Order should be: Abstract, Introduction, Methodology, Results, Discussion, Conclusion
        const sectionOrder: Record<string, number> = {
          'abstract': 1,
          'introduction': 2,
          'methodology': 3,
          'results': 4,
          'discussion': 5,
          'conclusion': 6
        };
        return (sectionOrder[a.id] || 99) - (sectionOrder[b.id] || 99);
      })
      .map(section => `## ${section.name}\n\n${section.content}`)
      .join('\n\n');
    
    // Get references section separately to ensure it's always at the end
    let referencesSection = completedSections.find(s => s.id === 'references');
    
    // Check for placeholder text in references and fix it if found
    let referencesContent = referencesSection?.content || '';
    
    // Validate references content and use default if needed
    if (!referencesContent || 
        referencesContent.trim() === '' ||
        referencesContent.includes('Please provide the content') || 
        referencesContent.includes('To generate a complete') ||
        referencesContent.includes('## References') ||
        referencesContent.includes('References:') ||
        referencesContent.includes('no citations were found')) {
      console.log('Invalid references content detected - using default references');
      referencesContent = defaultAcademicReferences;
      
      // Update the references section with fixed content if it exists
      if (referencesSection) {
        setGeneratedSections(prev => prev.map(s => 
          s.id === 'references' ? { ...s, content: referencesContent, status: 'completed' as const } : s
        ));
      } else {
        // Create a new references section if none exists
        const newReferencesSection: GeneratedSection = {
          id: 'references',
          name: 'References', 
          description: 'Citations and references used throughout the paper',
          status: 'completed',
          content: defaultAcademicReferences,
          icon: SECTION_TYPES.find(t => t.id === 'references')?.icon
        };
        
        setGeneratedSections(prev => [...prev, newReferencesSection]);
        referencesSection = newReferencesSection;
      }
    }
    
    // Clean up references content
    // Remove any "References:" header that might be at the beginning
    referencesContent = referencesContent.replace(/^References:[ \t]*[\r\n]+/, '');
    
    // Combine main content with references section always at the end
    // Always include references section, even if we have to use default references
    const formattedContent = mainSections + `\n\n## References\n\n${referencesContent}`;
    
    try {
      // Instead of trying to navigate, we'll switch to the editor view within this component
      setEditorContent({
        title: userInputs.metadata.title,
        content: formattedContent
      });
      
      // Store the content in localStorage as a backup
      editorService.sendToMainEditor({
        title: userInputs.metadata.title,
        content: formattedContent
      });
      
      // Update UI state to show the editor
      setCurrentStep('editor');
      toast.success("Paper loaded in editor for further editing.");
    } catch (error) {
      console.error('Error loading editor:', error);
      toast.error("An error occurred while loading the editor.");
    }
  };

  // Render the appropriate step based on currentStep
  if (currentStep === 'editor' && editorContent) {
    return (
      <div className="min-h-screen">
        <div className="sticky top-0 z-10 bg-white p-4 border-b shadow-sm flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentStep('generation')}
              className="flex items-center gap-1"
            >
              ← Back to Paper
            </Button>
            <h2 className="font-bold text-lg text-gray-800">{editorContent.title}</h2>
          </div>
          <Button
            variant="default"
            size="sm"
            className="bg-green-600 hover:bg-green-700"
          >
            Save Changes
          </Button>
        </div>
        <MainEditor initialTitle={editorContent.title} initialContent={editorContent.content} />
      </div>
    );
  }

  // Render input step
  if (currentStep === 'input') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 p-6">
        <div className="max-w-6xl mx-auto space-y-8">
          <div className="text-center mb-12">
            <div className="flex items-center justify-center mb-6">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full blur-lg opacity-20"></div>
                <div className="relative bg-gradient-to-r from-blue-600 to-purple-600 p-4 rounded-full">
                  <Bot className="h-10 w-10 text-white" />
                </div>
              </div>
            </div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
              AI Research Paper Generator
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
              Upload your research content with methodology, results, and analysis. Our AI will craft a complete, professional research paper for you.
            </p>
          </div>

          {/* Paper Metadata Section */}
          <PaperMetadataForm 
            metadata={userInputs.metadata}
            updateMetadata={updateMetadata}
          />

          {/* Research Content Sections */}
          <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader className="pb-8">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-3xl flex items-center gap-4">
                    <div className="w-3 h-10 bg-gradient-to-b from-green-500 to-blue-500 rounded-full"></div>
                    Research Content Sections
                  </CardTitle>
                  <CardDescription className="text-lg mt-3 text-gray-600">
                    Build comprehensive research sections with detailed analysis, figures, and supporting content.
                  </CardDescription>
                </div>
                <div className="hidden lg:flex flex-wrap gap-3 max-w-md">
                  {SECTION_TYPES.sort((a, b) => a.order - b.order).map((sectionType) => (
                    <Button 
                      key={sectionType.id}
                      onClick={() => addUserSection(sectionType.id)} 
                      variant="outline" 
                      className="flex items-center gap-2 hover:shadow-lg transition-all duration-200 border-2 hover:border-blue-300 px-4 py-2"
                    >
                      <div className={`w-3 h-3 rounded-full ${sectionType.color}`}></div>
                      <sectionType.icon className="h-4 w-4" />
                      <span className="font-medium">{sectionType.name}</span>
                      {sectionType.required && (
                        <Badge variant="secondary" className="bg-red-100 text-red-800 ml-1">Required</Badge>
                      )}
                    </Button>
                  ))}
                </div>
              </div>
              
              {/* Mobile Section Buttons */}
              <div className="lg:hidden flex flex-wrap gap-3 mt-6">
                {SECTION_TYPES.map((sectionType) => (
                  <Button 
                    key={sectionType.id}
                    onClick={() => addUserSection(sectionType.id)} 
                    variant="outline" 
                    className="flex items-center gap-2 hover:shadow-lg transition-all duration-200 border-2 hover:border-blue-300"
                  >
                    <div className={`w-3 h-3 rounded-full ${sectionType.color}`}></div>
                    <sectionType.icon className="h-4 w-4" />
                    {sectionType.name}
                  </Button>
                ))}
              </div>
            </CardHeader>
            
            <CardContent>
              {userInputs.userSections.length === 0 ? (
                <div className="text-center py-20 bg-gradient-to-br from-gray-50 to-blue-50 rounded-2xl border-2 border-dashed border-gray-200">
                  <div className="relative mb-8">
                    <div className="absolute inset-0 bg-blue-100 rounded-full blur-2xl opacity-40"></div>
                    <GripVertical className="relative h-20 w-20 mx-auto text-gray-400" />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-700 mb-3">Ready to Build Your Research?</h3>
                  <p className="text-gray-500 mb-8 max-w-md mx-auto leading-relaxed">
                    Add research sections to include your methodology, results, analysis, and supporting figures. Each section supports rich content and detailed analysis.
                  </p>
                  <div className="flex justify-center gap-4">
                    <Button 
                      onClick={() => addUserSection('introduction')}
                      className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 px-6 py-3"
                    >
                      {(() => {
                        const IntroIcon = SECTION_TYPES.find(s => s.id === 'introduction')?.icon;
                        return IntroIcon && <IntroIcon className="h-5 w-5" />;
                      })()}
                      Start with Introduction
                    </Button>
                    <Button 
                      onClick={() => addUserSection('methodology')}
                      variant="outline"
                      className="flex items-center gap-2 border-2 hover:border-green-400 px-6 py-3"
                    >
                      {(() => {
                        const MethodIcon = SECTION_TYPES.find(s => s.id === 'methodology')?.icon;
                        return MethodIcon && <MethodIcon className="h-5 w-5" />;
                      })()}
                      Start with Methodology
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="space-y-10">
                  {/* Sort sections in logical order based on the SECTION_TYPES order property */}
                  {userInputs.userSections
                    .sort((a, b) => {
                      const aType = SECTION_TYPES.find(t => t.name === a.name);
                      const bType = SECTION_TYPES.find(t => t.name === b.name);
                      return (aType?.order || 0) - (bType?.order || 0);
                    })
                    .map((section) => {
                      const sectionType = SECTION_TYPES.find(s => s.name === section.name);
                      if (!sectionType) return null;
                      
                      return (
                        <SectionCard
                          key={section.id}
                          section={section}
                          sectionColor={sectionType.color}
                          sectionIcon={sectionType.icon}
                          sectionDescription={sectionType.description}
                          removeUserSection={removeUserSection}
                          addContentItem={addContentItem}
                          updateContentItem={updateContentItem}
                          removeContentItem={removeContentItem}
                          moveContentItem={moveContentItem}
                          analyzingItems={analyzingItems}
                          setAnalyzingItems={setAnalyzingItems}
                          selectedModel={selectedModel}
                        />
                      );
                    })}
                </div>
              )}
            </CardContent>
          </Card>

          {/* AI Model Selection and Generate Button */}
          <div className="bg-white/70 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-gray-200 space-y-6">
            <h3 className="text-xl font-bold text-gray-800 mb-4">AI Model Selection</h3>
            <AIModelSelector 
              model={selectedModel} 
              models={AI_MODELS} 
              setModel={setSelectedModel} 
            />
            
            <div className="text-center pt-8">
              <Button 
                onClick={generateAllSections} 
                disabled={!canProceed}
                size="lg"
                className="px-12 py-6 text-lg bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 shadow-xl hover:shadow-2xl transition-all duration-300"
              >
                <Sparkles className="h-6 w-6 mr-3" />
                Generate Complete Research Paper
              </Button>
              <div className="mt-4">
                {!canProceed ? (
                  <p className="text-red-500 text-lg font-medium">
                    Please provide a title for your research paper
                  </p>
                ) : (
                  <p className="text-gray-600 text-base">
                    Only a title is required. Additional content will enhance the quality of generated paper.
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Render generation step if not in editor mode
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 p-6">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-6">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full blur-lg opacity-20 animate-pulse"></div>
              <div className="relative bg-gradient-to-r from-blue-600 to-purple-600 p-4 rounded-full">
                <Bot className="h-10 w-10 text-white" />
              </div>
            </div>
          </div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
            {isGenerating ? "Generating Your Research Paper" : "Research Paper Generated"}
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            {isGenerating 
              ? "Our AI is carefully crafting each section of your research paper step by step" 
              : "All sections of your research paper have been generated successfully"}
          </p>
        </div>

        <GenerationPanel 
          generatedSections={generatedSections}
          isGenerating={isGenerating}
          onExport={handleExport}
          onEditInEditor={handleEditInMainEditor}
          paperMetadata={userInputs.metadata}
        />
      </div>
    </div>
  );
}
